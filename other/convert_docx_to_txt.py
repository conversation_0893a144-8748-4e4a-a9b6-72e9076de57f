import os
import random
from pathlib import Path
from docx import Document

def convert_docx_to_txt(docx_path, txt_path):
    """将docx文件转换为txt文件，保持内容完整"""
    try:
        doc = Document(docx_path)
        content = []
        
        # 提取所有段落内容
        for paragraph in doc.paragraphs:
            if paragraph.text.strip():  # 跳过空段落
                content.append(paragraph.text)
        
        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    if cell.text.strip():
                        row_text.append(cell.text.strip())
                if row_text:
                    content.append('\t'.join(row_text))
        
        # 写入txt文件
        with open(txt_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(content))
        
        return True
    except Exception as e:
        print(f"转换文件 {docx_path} 时出错: {e}")
        return False

def main():
    # 使用绝对路径
    datasets_law_dir = Path("/Users/<USER>/Documents/School work/chunking_evaluation/chunking_evaluation/datasets/datasets_law")
    output_dir = Path("/Users/<USER>/Documents/School work/chunking_evaluation/chunking_evaluation/datasets/法律文件")
    
    print(f"查找目录: {datasets_law_dir}")
    print(f"目录是否存在: {datasets_law_dir.exists()}")
    
    if not datasets_law_dir.exists():
        print("目录不存在！")
        return
    
    # 确保输出目录存在
    output_dir.mkdir(parents=True, exist_ok=True)
    
    # 获取所有docx文件
    docx_files = list(datasets_law_dir.glob("*.docx"))
    
    if len(docx_files) == 0:
        print("没有找到docx文件，显示目录内容:")
        for item in datasets_law_dir.iterdir():
            print(f"  - {item.name}")
        return
    
    # 随机选择5个文件
    num_files = min(5, len(docx_files))
    selected_files = random.sample(docx_files, num_files)
    
    print(f"找到 {len(docx_files)} 个docx文件，随机选择 {num_files} 个进行转换")
    
    # 转换文件
    success_count = 0
    for docx_file in selected_files:
        txt_filename = docx_file.stem + ".txt"
        txt_path = output_dir / txt_filename
        
        print(f"正在转换: {docx_file.name} -> {txt_filename}")
        
        if convert_docx_to_txt(docx_file, txt_path):
            success_count += 1
            print(f"✓ 成功转换: {txt_filename}")
        else:
            print(f"✗ 转换失败: {docx_file.name}")
    
    print(f"\n转换完成！成功转换 {success_count}/{num_files} 个文件")

if __name__ == "__main__":
    main()
