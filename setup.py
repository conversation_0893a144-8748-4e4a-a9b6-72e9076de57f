"""
文本分块评估包的安装配置文件

这个文件定义了chunking_evaluation包的安装配置，包括依赖项、元数据和打包信息。
该包提供了多种文本分块方法的实现和评估框架，主要用于RAG系统中的文本预处理。

主要功能：
1. 提供多种文本分块算法（固定长度、语义分块、LLM分块等）
2. 提供完整的分块效果评估框架
3. 支持合成数据生成和质量过滤
4. 包含预定义的基准数据集用于标准化评估
"""

from setuptools import setup, find_packages

setup(
    # 包的基本信息
    name="chunking_evaluation",  # 包名称
    version="0.1.0",  # 版本号
    packages=find_packages(),  # 自动发现所有Python包
    
    # 依赖项列表 - 运行此包所需的第三方库
    install_requires=[
        "tiktoken",  # OpenAI的tokenizer，用于精确计算token数量
        "fuzzywuzzy",  # 模糊字符串匹配库，用于文本搜索和匹配
        "pandas",  # 数据处理和分析库，用于处理评估数据
        "numpy",  # 数值计算库，用于向量运算和统计计算
        "tqdm",  # 进度条库，用于显示长时间运行任务的进度
        "chromadb",  # 向量数据库，用于存储和检索文档嵌入
        "python-Levenshtein",  # 字符串相似度计算库，提升fuzzywuzzy性能
        "openai",  # OpenAI API客户端，用于调用GPT和embedding服务
        "anthropic",  # Anthropic API客户端，用于调用Claude模型
        "attrs"  # 类定义辅助库，简化类的创建和管理
    ],
    
    # 作者信息
    author="Brandon A. Smith",  # 作者姓名
    author_email="<EMAIL>",  # 作者邮箱
    
    # 包描述信息
    description="一个用于评估多种文本分块方法的Python包，同时提供了两种新的分块方法。",
    long_description=open("README.md").read(),  # 从README文件读取详细描述
    long_description_content_type="text/markdown",  # 详细描述的格式
    
    # 项目链接
    url="https://github.com/yourusername/chunking_evaluation",  # 项目主页URL
    
    # 分类标签 - 帮助用户在PyPI上找到这个包
    classifiers=[
        "Programming Language :: Python :: 3",  # 支持Python 3
        "License :: OSI Approved :: MIT License",  # 使用MIT许可证
        "Operating System :: OS Independent",  # 跨平台兼容
    ],
    
    # 包数据配置
    include_package_data=True,  # 包含非Python文件
    package_data={
        # 指定要包含的数据文件
        'chunking_evaluation': [
            'evaluation_framework/general_evaluation_data/**/*',  # 通用评估数据文件
            'evaluation_framework/prompts/**/*'  # 提示模板文件
        ]
    },
    
    # Python版本要求
    python_requires='>=3.6',  # 最低支持Python 3.6
)
