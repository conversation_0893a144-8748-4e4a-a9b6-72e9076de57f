"""
通用评估模块

这个模块提供了基于预定义基准数据集的通用评估功能。
它使用内置的语料库和问题集来评估分块策略的性能。
"""

from .base_evaluation import BaseEvaluation
from importlib import resources
from pathlib import Path

class GeneralEvaluation(BaseEvaluation):
    """
    通用评估类
    
    这个类继承自BaseEvaluation，专门用于运行基于预定义基准数据集的评估。
    它自动加载内置的语料库和问题数据，无需用户手动准备数据。
    
    主要特点：
    - 使用预定义的高质量基准数据集
    - 包含多个不同类型的语料库
    - 提供标准化的评估结果，便于比较不同分块策略
    """
    
    def __init__(self, chroma_db_path=None):
        """
        初始化通用评估类
        
        参数:
            chroma_db_path (str, 可选): ChromaDB数据库的存储路径，用于缓存嵌入向量
        """
        # 使用资源管理器访问内置的评估数据
        with resources.as_file(resources.files('chunking_evaluation.evaluation_framework') / 'general_evaluation_data') as general_benchmark_path:
            # 保存基准数据路径的引用
            self.general_benchmark_path = general_benchmark_path
            
            # 构建问题数据集的完整路径
            questions_df_path = self.general_benchmark_path / 'questions_df.csv'

            # 获取语料库文件夹路径
            corpora_folder_path = self.general_benchmark_path / 'corpora'
            # 列出语料库文件夹中的所有文件
            corpora_filenames = [f for f in corpora_folder_path.iterdir() if f.is_file()]

            # 创建语料库ID到文件路径的映射字典
            # 使用文件名（不含扩展名）作为语料库ID
            corpora_id_paths = {
                f.stem: str(f) for f in corpora_filenames
            }

            # 调用父类构造函数，传入问题数据路径和语料库路径映射
            super().__init__(str(questions_df_path), chroma_db_path=chroma_db_path, corpora_id_paths=corpora_id_paths)

            # 标记这是通用评估，用于启用特殊的优化功能
            self.is_general = True
