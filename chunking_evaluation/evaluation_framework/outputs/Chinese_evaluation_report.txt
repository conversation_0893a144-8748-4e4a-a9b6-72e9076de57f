中文数据集分块评估报告 - 多参数配置性能分析
================================================================================

📊 成功评估的配置:
--------------------------------------------------

🔍 FixedTokenChunker:
  配置: FixedTokenChunker_chunk_overlap20_chunk_size200
  参数: chunk_size=200, chunk_overlap=20
  精确率: 0.098
  召回率: 0.978
  IoU分数: 0.098
  ----------------------------------------
  配置: FixedTokenChunker_chunk_overlap50_chunk_size200
  参数: chunk_size=200, chunk_overlap=50
  精确率: 0.098
  召回率: 0.978
  IoU分数: 0.098
  ----------------------------------------
  配置: FixedTokenChunker_chunk_overlap50_chunk_size500
  参数: chunk_size=500, chunk_overlap=50
  精确率: 0.032
  召回率: 0.933
  IoU分数: 0.032
  ----------------------------------------
  配置: FixedTokenChunker_chunk_overlap100_chunk_size500
  参数: chunk_size=500, chunk_overlap=100
  精确率: 0.032
  召回率: 0.933
  IoU分数: 0.032
  ----------------------------------------
  配置: FixedTokenChunker_chunk_overlap100_chunk_size1000
  参数: chunk_size=1000, chunk_overlap=100
  精确率: 0.020
  召回率: 0.999
  IoU分数: 0.020
  ----------------------------------------
  配置: FixedTokenChunker_chunk_overlap200_chunk_size1000
  参数: chunk_size=1000, chunk_overlap=200
  精确率: 0.020
  召回率: 0.999
  IoU分数: 0.020
  ----------------------------------------
  🏆 最佳IoU配置: FixedTokenChunker_chunk_overlap20_chunk_size200 (IoU: 0.098)
     参数: {'chunk_size': 200, 'chunk_overlap': 20}


🔍 RecursiveTokenChunker:
  配置: RecursiveTokenChunker_chunk_overlap20_chunk_size200
  参数: chunk_size=200, chunk_overlap=20
  精确率: 0.097
  召回率: 0.978
  IoU分数: 0.097
  ----------------------------------------
  配置: RecursiveTokenChunker_chunk_overlap50_chunk_size200
  参数: chunk_size=200, chunk_overlap=50
  精确率: 0.097
  召回率: 1.000
  IoU分数: 0.097
  ----------------------------------------
  配置: RecursiveTokenChunker_chunk_overlap50_chunk_size500
  参数: chunk_size=500, chunk_overlap=50
  精确率: 0.035
  召回率: 1.000
  IoU分数: 0.035
  ----------------------------------------
  配置: RecursiveTokenChunker_chunk_overlap100_chunk_size500
  参数: chunk_size=500, chunk_overlap=100
  精确率: 0.037
  召回率: 1.000
  IoU分数: 0.037
  ----------------------------------------
  配置: RecursiveTokenChunker_chunk_overlap100_chunk_size1000
  参数: chunk_size=1000, chunk_overlap=100
  精确率: 0.018
  召回率: 1.000
  IoU分数: 0.018
  ----------------------------------------
  配置: RecursiveTokenChunker_chunk_overlap200_chunk_size1000
  参数: chunk_size=1000, chunk_overlap=200
  精确率: 0.019
  召回率: 1.000
  IoU分数: 0.019
  ----------------------------------------
  🏆 最佳IoU配置: RecursiveTokenChunker_chunk_overlap50_chunk_size200 (IoU: 0.097)
     参数: {'chunk_size': 200, 'chunk_overlap': 50}


🏆 全局最佳表现:
--------------------------------------------------
最佳IoU分数: FixedTokenChunker_chunk_overlap20_chunk_size200 (0.098)
  参数: {'chunk_size': 200, 'chunk_overlap': 20}
最佳精确率: FixedTokenChunker_chunk_overlap20_chunk_size200 (0.098)
  参数: {'chunk_size': 200, 'chunk_overlap': 20}
最佳召回率: RecursiveTokenChunker_chunk_overlap50_chunk_size200 (1.000)
  参数: {'chunk_size': 200, 'chunk_overlap': 50}
