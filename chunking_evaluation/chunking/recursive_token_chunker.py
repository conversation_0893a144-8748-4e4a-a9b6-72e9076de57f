
"""
递归Token分块器模块

此脚本改编自 LangChain 包，由 LangChain AI 开发
原始代码可在以下位置找到: https://github.com/langchain-ai/langchain/blob/master/libs/text-splitters/langchain_text_splitters/character.py
许可证: MIT License

这个模块实现了递归字符分块功能，通过递归地尝试不同的分隔符来找到最适合的文本分割方式。
它特别适用于需要保持文本结构和语义完整性的场景，支持多种编程语言的特定分割策略。
"""

# 导入必要的模块和类型
from typing import Any, List, Optional  # 类型提示
from .base_chunker import BaseChunker  # 基础分块器类
from chunking_evaluation.utils import Language  # 语言枚举类型
from .fixed_token_chunker import TextSplitter  # 文本分割器基类
import re  # 正则表达式模块

def _split_text_with_regex(
    text: str, separator: str, keep_separator: bool
) -> List[str]:
    """
    使用正则表达式和指定分隔符分割文本
    
    这个函数根据给定的分隔符将文本分割成多个部分，可以选择是否保留分隔符。
    
    参数:
        text (str): 待分割的文本
        separator (str): 用于分割的分隔符（可以是正则表达式）
        keep_separator (bool): 是否在结果中保留分隔符
        
    返回:
        List[str]: 分割后的文本片段列表（过滤掉空字符串）
        
    算法逻辑:
        1. 如果有分隔符且需要保留分隔符，使用括号捕获组保留分隔符
        2. 如果有分隔符但不保留，直接使用re.split分割
        3. 如果没有分隔符，将文本拆分为单个字符
        4. 过滤掉空字符串
    """
    # Now that we have the separator, split the text
    if separator:  # 如果分隔符不为空
        if keep_separator:  # 如果需要保留分隔符
            # The parentheses in the pattern keep the delimiters in the result.
            # 使用括号捕获组，这样分隔符也会包含在结果中
            _splits = re.split(f"({separator})", text)
            # 将分隔符与后面的文本片段合并
            splits = [_splits[i] + _splits[i + 1] for i in range(1, len(_splits), 2)]
            # 如果分割结果数量为偶数，说明最后还有一个片段没有处理
            if len(_splits) % 2 == 0:
                splits += _splits[-1:]
            # 将第一个片段（在第一个分隔符之前的内容）添加到开头
            splits = [_splits[0]] + splits
        else:  # 如果不需要保留分隔符
            splits = re.split(separator, text)  # 直接分割
    else:  # 如果分隔符为空
        splits = list(text)  # 将文本拆分为单个字符列表
    
    # 过滤掉空字符串并返回
    return [s for s in splits if s != ""]

class RecursiveTokenChunker(TextSplitter):
    """
    递归Token分块器类
    
    这个类通过递归地查看不同的字符分隔符来分割文本。
    它会按照优先级顺序尝试不同的分隔符，直到找到一个有效的分割方式。
    
    工作原理:
    1. 按照分隔符优先级列表依次尝试分割
    2. 如果某个分隔符能够有效分割文本，就使用它
    3. 对于过长的文本块，递归使用下一级分隔符继续分割
    4. 最终将所有文本块合并到合适的大小
    
    主要特点:
    - 保持文本的自然结构（段落、句子、单词）
    - 支持多种编程语言的特定分割策略
    - 可以保留或丢弃分隔符
    - 支持正则表达式分隔符
    
    属性:
        _separators (List[str]): 分隔符优先级列表
        _is_separator_regex (bool): 分隔符是否为正则表达式
    """

    def __init__(
        self,
        chunk_size: int = 4000,
        chunk_overlap: int = 200,
        separators: Optional[List[str]] = None,
        keep_separator: bool = True,
        is_separator_regex: bool = False,
        **kwargs: Any,
    ) -> None:
        """
        初始化递归Token分块器
        
        参数:
            chunk_size (int): 目标文本块大小，默认4000
            chunk_overlap (int): 相邻块之间的重叠大小，默认200
            separators (Optional[List[str]]): 分隔符优先级列表，默认为常见的文本分隔符
            keep_separator (bool): 是否在分割时保留分隔符，默认True
            is_separator_regex (bool): 分隔符是否为正则表达式，默认False
            **kwargs: 传递给父类的其他参数
            
        默认分隔符优先级（从高到低）:
            1. "\n\n" - 段落分隔符（双换行）
            2. "\n" - 行分隔符（单换行）
            3. "." - 句号（句子结束）
            4. "?" - 问号（疑问句结束）
            5. "!" - 感叹号（感叹句结束）
            6. " " - 空格（单词分隔）
            7. "" - 空字符串（字符级分割）
        """
        # 调用父类构造函数
        super().__init__(chunk_size=chunk_size, chunk_overlap=chunk_overlap, keep_separator=keep_separator, **kwargs)
        
        # 设置默认分隔符列表（按优先级从高到低排列）
        self._separators = separators or ["\n\n", "\n", ".", "?", "!", " ", ""]
        
        # 设置分隔符是否为正则表达式
        self._is_separator_regex = is_separator_regex

    def _split_text(self, text: str, separators: List[str]) -> List[str]:
        """
        递归分割文本的核心方法
        
        这个方法实现了递归分割的核心逻辑：
        1. 按优先级尝试不同的分隔符
        2. 找到有效分隔符后进行分割
        3. 对过长的片段递归使用下一级分隔符
        4. 将合适大小的片段合并成最终的文本块
        
        参数:
            text (str): 待分割的文本
            separators (List[str]): 当前可用的分隔符列表
            
        返回:
            List[str]: 分割后的文本块列表
            
        算法流程:
            1. 遍历分隔符列表，找到第一个在文本中存在的分隔符
            2. 使用找到的分隔符分割文本
            3. 检查每个分割片段的长度
            4. 对于合适长度的片段，加入待合并列表
            5. 对于过长的片段，递归使用下一级分隔符继续分割
            6. 将待合并的片段合并成最终的文本块
        """
        final_chunks = []  # 存储最终的文本块
        
        # Get appropriate separator to use
        # 获取要使用的合适分隔符（默认使用最后一个，即空字符串）
        separator = separators[-1]
        new_separators = []  # 存储下一轮递归要使用的分隔符列表
        
        # 遍历分隔符列表，找到第一个在文本中存在的分隔符
        for i, _s in enumerate(separators):
            # 如果不是正则表达式，需要转义特殊字符
            _separator = _s if self._is_separator_regex else re.escape(_s)
            
            # 如果分隔符为空字符串，直接使用（字符级分割）
            if _s == "":
                separator = _s
                break
                
            # 检查当前分隔符是否在文本中存在
            if re.search(_separator, text):
                separator = _s  # 使用当前分隔符
                new_separators = separators[i + 1 :]  # 设置下一轮递归的分隔符列表
                break

        # 准备用于正则表达式的分隔符
        _separator = separator if self._is_separator_regex else re.escape(separator)
        # 使用选定的分隔符分割文本
        splits = _split_text_with_regex(text, _separator, self._keep_separator)

        # Now go merging things, recursively splitting longer texts.
        # 现在开始合并处理，对较长的文本进行递归分割
        _good_splits = []  # 存储长度合适的文本片段
        # 确定合并时使用的分隔符
        _separator = "" if self._keep_separator else separator
        
        # 遍历每个分割片段
        for s in splits:
            # 如果片段长度小于目标块大小，加入待合并列表
            if self._length_function(s) < self._chunk_size:
                _good_splits.append(s)
            else:
                # 如果片段过长，需要进一步处理
                
                # 首先处理之前积累的合适长度片段
                if _good_splits:
                    merged_text = self._merge_splits(_good_splits, _separator)
                    final_chunks.extend(merged_text)
                    _good_splits = []  # 清空待合并列表
                
                # 对过长片段进行处理
                if not new_separators:
                    # 如果没有更多分隔符可用，直接添加（即使过长）
                    final_chunks.append(s)
                else:
                    # 递归使用下一级分隔符继续分割
                    other_info = self._split_text(s, new_separators)
                    final_chunks.extend(other_info)
        
        # 处理最后剩余的合适长度片段
        if _good_splits:
            merged_text = self._merge_splits(_good_splits, _separator)
            final_chunks.extend(merged_text)
            
        return final_chunks

    def split_text(self, text: str) -> List[str]:
        """
        公共接口：分割文本
        
        这是对外提供的主要接口，实现了BaseChunker的抽象方法。
        它调用内部的_split_text方法来执行实际的递归分割逻辑。
        
        参数:
            text (str): 待分割的原始文本
            
        返回:
            List[str]: 分割后的文本块列表
            
        使用示例:
            chunker = RecursiveTokenChunker(chunk_size=1000)
            chunks = chunker.split_text("这是一段很长的文本...")
        """
        return self._split_text(text, self._separators)

    # @classmethod
    # def from_language(
    #     cls, language: Language, **kwargs: Any
    # ) -> RecursiveCharacterTextSplitter:
    #     separators = cls.get_separators_for_language(language)
    #     return cls(separators=separators, is_separator_regex=True, **kwargs)

    @staticmethod
    def get_separators_for_language(language: Language) -> List[str]:
        """
        根据编程语言获取特定的分隔符列表
        
        这个静态方法为不同的编程语言提供了优化的分隔符列表。
        每种语言的分隔符都按照该语言的语法结构和最佳实践进行排序。
        
        参数:
            language (Language): 编程语言枚举值
            
        返回:
            List[str]: 针对该语言优化的分隔符列表
            
        异常:
            ValueError: 如果提供的语言不被支持
            
        支持的语言:
            - CPP: C++语言
            - GO: Go语言
            - JAVA: Java语言
            - KOTLIN: Kotlin语言
            - JS: JavaScript语言
            - TS: TypeScript语言
            - PHP: PHP语言
            - PROTO: Protocol Buffers
            - PYTHON: Python语言
            - RST: reStructuredText
            - RUBY: Ruby语言
            - RUST: Rust语言
            - SCALA: Scala语言
            - SWIFT: Swift语言
            - MARKDOWN: Markdown格式
            - LATEX: LaTeX格式
            - HTML: HTML格式
            - CSHARP: C#语言
            - SOL: Solidity语言
            - COBOL: COBOL语言
        """
        if language == Language.CPP:
            # C++语言的分隔符列表（按优先级从高到低）
            return [
                # Split along class definitions
                "\nclass ",      # 类定义 - 最高优先级，因为类是C++的主要组织单位
                # Split along function definitions
                "\nvoid ",       # void函数定义
                "\nint ",        # int函数定义
                "\nfloat ",      # float函数定义
                "\ndouble ",     # double函数定义
                # Split along control flow statements
                "\nif ",         # if条件语句
                "\nfor ",        # for循环语句
                "\nwhile ",      # while循环语句
                "\nswitch ",     # switch选择语句
                "\ncase ",       # case分支语句
                # Split by the normal type of lines
                "\n\n",          # 双换行 - 段落分隔
                "\n",            # 单换行 - 行分隔
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.GO:
            # Go语言的分隔符列表（按优先级从高到低）
            return [
                # Split along function definitions
                # 按函数定义分割
                "\nfunc ",      # 函数定义 - Go语言的主要代码组织单位
                "\nvar ",       # 变量声明块
                "\nconst ",     # 常量声明块
                "\ntype ",      # 类型定义（结构体、接口等）
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nfor ",       # for循环语句（Go中唯一的循环结构）
                "\nswitch ",    # switch选择语句
                "\ncase ",      # case分支语句
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.JAVA:
            # Java语言的分隔符列表（按优先级从高到低）
            return [
                # Split along class definitions
                # 按类定义分割
                "\nclass ",     # 类定义 - Java的核心组织单位
                # Split along method definitions
                # 按方法定义分割（通过访问修饰符识别）
                "\npublic ",    # public方法/字段
                "\nprotected ", # protected方法/字段
                "\nprivate ",   # private方法/字段
                "\nstatic ",    # static方法/字段
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nfor ",       # for循环语句
                "\nwhile ",     # while循环语句
                "\nswitch ",    # switch选择语句
                "\ncase ",      # case分支语句
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.KOTLIN:
            # Kotlin语言的分隔符列表（按优先级从高到低）
            return [
                # Split along class definitions
                # 按类定义分割
                "\nclass ",     # 类定义 - Kotlin的主要组织单位
                # Split along method definitions
                # 按方法和属性定义分割
                "\npublic ",    # public访问修饰符
                "\nprotected ", # protected访问修饰符
                "\nprivate ",   # private访问修饰符
                "\ninternal ",  # internal访问修饰符（Kotlin特有）
                "\ncompanion ", # companion object（伴生对象）
                "\nfun ",       # 函数定义（Kotlin关键字）
                "\nval ",       # 不可变属性定义
                "\nvar ",       # 可变属性定义
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nfor ",       # for循环语句
                "\nwhile ",     # while循环语句
                "\nwhen ",      # when表达式（Kotlin的switch替代）
                "\ncase ",      # case分支（虽然Kotlin用when，但保留兼容性）
                "\nelse ",      # else分支
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.JS:
            # JavaScript语言的分隔符列表（按优先级从高到低）
            return [
                # Split along function definitions
                "\nfunction ",   # 函数声明 - 高优先级
                "\nconst ",      # const常量声明（ES6+）
                "\nlet ",        # let变量声明（ES6+）
                "\nvar ",        # var变量声明（传统）
                "\nclass ",      # 类声明（ES6+）
                # Split along control flow statements
                "\nif ",         # if条件语句
                "\nfor ",        # for循环语句
                "\nwhile ",      # while循环语句
                "\nswitch ",     # switch选择语句
                "\ncase ",       # case分支语句
                "\ndefault ",    # default默认分支
                # Split by the normal type of lines
                "\n\n",          # 双换行 - 段落分隔
                "\n",            # 单换行 - 行分隔
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.TS:
            # TypeScript语言的分隔符列表（按优先级从高到低）
            return [
                # TypeScript特有的类型定义（最高优先级）
                "\nenum ",      # 枚举定义 - TypeScript特有的类型
                "\ninterface ", # 接口定义 - TypeScript的核心特性
                "\nnamespace ", # 命名空间定义 - 组织代码的重要结构
                "\ntype ",      # 类型别名定义 - TypeScript类型系统
                # Split along class definitions
                # 按类定义分割
                "\nclass ",     # 类定义 - 面向对象编程的核心
                # Split along function definitions
                # 按函数定义分割
                "\nfunction ",  # 函数声明
                "\nconst ",     # const常量声明（ES6+，TypeScript推荐）
                "\nlet ",       # let变量声明（ES6+，块级作用域）
                "\nvar ",       # var变量声明（传统，不推荐但仍支持）
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nfor ",       # for循环语句
                "\nwhile ",     # while循环语句
                "\nswitch ",    # switch选择语句
                "\ncase ",      # case分支语句
                "\ndefault ",   # default默认分支
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.PHP:
            # PHP语言的分隔符列表（按优先级从高到低）
            return [
                # Split along function definitions
                # 按函数定义分割
                "\nfunction ",  # 函数定义 - PHP的主要代码组织方式
                # Split along class definitions
                # 按类定义分割
                "\nclass ",     # 类定义 - PHP面向对象编程的核心
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nforeach ",   # foreach循环 - PHP处理数组的常用方式
                "\nwhile ",     # while循环语句
                "\ndo ",        # do-while循环语句
                "\nswitch ",    # switch选择语句
                "\ncase ",      # case分支语句
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.PROTO:
            # Protocol Buffers的分隔符列表（按优先级从高到低）
            return [
                # Split along message definitions
                # 按消息定义分割
                "\nmessage ",   # 消息定义 - Protocol Buffers的核心数据结构
                # Split along service definitions
                # 按服务定义分割
                "\nservice ",   # 服务定义 - gRPC服务接口定义
                # Split along enum definitions
                # 按枚举定义分割
                "\nenum ",      # 枚举定义 - 定义常量集合
                # Split along option definitions
                # 按选项定义分割
                "\noption ",    # 选项定义 - 配置Protocol Buffers行为
                # Split along import statements
                # 按导入语句分割
                "\nimport ",    # 导入语句 - 引用其他.proto文件
                # Split along syntax declarations
                # 按语法声明分割
                "\nsyntax ",    # 语法声明 - 指定Protocol Buffers版本
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.PYTHON:
            # Python语言的分隔符列表（按优先级从高到低）
            return [
                # First, try to split along class definitions
                "\nclass ",      # 类定义 - 最高优先级，Python的主要组织单位
                "\ndef ",        # 函数定义（顶级函数）
                "\n\tdef ",      # 缩进的函数定义（类方法或嵌套函数）
                # Now split by the normal type of lines
                "\n\n",          # 双换行 - 段落分隔，Python中常用于分隔逻辑块
                "\n",            # 单换行 - 行分隔，Python的基本语句分隔
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.RST:
            # reStructuredText格式的分隔符列表（按优先级从高到低）
            return [
                # Split along section titles
                # 按章节标题分割
                "\n=+\n",       # 等号下划线 - RST的主标题标记
                "\n-+\n",       # 短横线下划线 - RST的副标题标记
                "\n\\*+\n",     # 星号下划线 - RST的三级标题标记
                # Split along directive markers
                # 按指令标记分割
                "\n\n.. *\n\n", # RST指令标记 - 如.. code-block::, .. note::等
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔，RST的基本段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.RUBY:
            # Ruby语言的分隔符列表（按优先级从高到低）
            return [
                # Split along method definitions
                # 按方法定义分割
                "\ndef ",       # 方法定义 - Ruby的主要代码组织方式
                "\nclass ",     # 类定义 - Ruby面向对象编程的核心
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nunless ",    # unless条件语句 - Ruby特有的反向条件
                "\nwhile ",     # while循环语句
                "\nfor ",       # for循环语句
                "\ndo ",        # do块开始 - Ruby的块语法
                "\nbegin ",     # begin异常处理块
                "\nrescue ",    # rescue异常捕获 - Ruby的异常处理
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.RUST:
            # Rust语言的分隔符列表（按优先级从高到低）
            return [
                # Split along function definitions
                # 按函数定义分割
                "\nfn ",        # 函数定义 - Rust的主要代码组织方式
                "\nconst ",     # 常量定义 - 编译时常量
                "\nlet ",       # 变量绑定 - Rust的变量声明方式
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nwhile ",     # while循环语句
                "\nfor ",       # for循环语句
                "\nloop ",      # loop无限循环 - Rust特有的循环结构
                "\nmatch ",     # match模式匹配 - Rust的强大特性
                "\nconst ",     # 重复的const（可能是为了兼容性）
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.SCALA:
            # Scala语言的分隔符列表（按优先级从高到低）
            return [
                # Split along class definitions
                # 按类定义分割
                "\nclass ",     # 类定义 - Scala面向对象编程的核心
                "\nobject ",    # 对象定义 - Scala的单例对象，函数式编程特性
                # Split along method definitions
                # 按方法和变量定义分割
                "\ndef ",       # 方法定义 - Scala的函数定义关键字
                "\nval ",       # 不可变值定义 - Scala推荐的函数式编程方式
                "\nvar ",       # 可变变量定义 - 传统的命令式编程方式
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nfor ",       # for推导式 - Scala的强大循环结构
                "\nwhile ",     # while循环语句
                "\nmatch ",     # match模式匹配 - Scala的核心特性
                "\ncase ",      # case分支 - 用于match表达式和偏函数
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.SWIFT:
            # Swift语言的分隔符列表（按优先级从高到低）
            return [
                # Split along function definitions
                # 按函数定义分割
                "\nfunc ",      # 函数定义 - Swift的主要代码组织方式
                # Split along class definitions
                # 按类型定义分割
                "\nclass ",     # 类定义 - 引用类型，支持继承
                "\nstruct ",    # 结构体定义 - 值类型，Swift推荐使用
                "\nenum ",      # 枚举定义 - Swift的强大枚举系统
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",        # if条件语句
                "\nfor ",       # for-in循环语句
                "\nwhile ",     # while循环语句
                "\ndo ",        # do语句块或do-catch异常处理
                "\nswitch ",    # switch选择语句 - Swift的强大模式匹配
                "\ncase ",      # case分支语句
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",         # 双换行 - 段落分隔
                "\n",           # 单换行 - 行分隔
                " ",            # 空格 - 单词分隔
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.MARKDOWN:
            # Markdown格式的分隔符列表（按优先级从高到低）
            return [
                # First, try to split along Markdown headings (starting with level 2)
                "\n#{1,6} ",     # Markdown标题（1-6级标题）- 最高优先级
                # Note the alternative syntax for headings (below) is not handled here
                # Heading level 2
                # ---------------
                # End of code block
                "```\n",         # 代码块结束标记 - 重要的结构分隔符
                # Horizontal lines
                "\n\\*\\*\\*+\n", # 水平分割线（星号）
                "\n---+\n",      # 水平分割线（短横线）
                "\n___+\n",      # 水平分割线（下划线）
                # Note that this splitter doesn't handle horizontal lines defined
                # by *three or more* of ***, ---, or ___, but this is not handled
                "\n\n",          # 双换行 - 段落分隔，Markdown的基本段落分隔
                "\n",            # 单换行 - 行分隔
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.LATEX:
            return [
                # First, try to split along Latex sections
                # 首先按LaTeX章节分割
                "\n\\\\chapter{",      # 章节标题 - 最高级别的文档结构
                "\n\\\\section{",      # 节标题 - 主要的文档分割
                "\n\\\\subsection{",   # 小节标题 - 次级文档分割
                "\n\\\\subsubsection{", # 子小节标题 - 三级文档分割
                # Now split by environments
                # 按LaTeX环境分割
                "\n\\\\begin{enumerate}",   # 有序列表环境
                "\n\\\\begin{itemize}",     # 无序列表环境
                "\n\\\\begin{description}", # 描述列表环境
                "\n\\\\begin{list}",        # 通用列表环境
                "\n\\\\begin{quote}",       # 引用环境
                "\n\\\\begin{quotation}",   # 长引用环境
                "\n\\\\begin{verse}",       # 诗歌环境
                "\n\\\\begin{verbatim}",    # 原样输出环境
                # Now split by math environments
                # 按数学环境分割
                "\n\\\begin{align}",        # 对齐数学公式环境
                "$$",
                "$",
                # Now split by the normal type of lines
                " ",
                "",
            ]
        elif language == Language.HTML:
            # HTML格式的分隔符列表（按优先级从高到低）
            return [
                # First, try to split along HTML tags
                # 首先按HTML标签分割
                "<body",        # body标签 - HTML文档主体
                "<div",         # div标签 - 通用容器，最常用的布局元素
                "<p",           # p标签 - 段落标签
                "<br",          # br标签 - 换行标签
                "<li",          # li标签 - 列表项
                "<h1",          # h1标签 - 一级标题
                "<h2",          # h2标签 - 二级标题
                "<h3",          # h3标签 - 三级标题
                "<h4",          # h4标签 - 四级标题
                "<h5",          # h5标签 - 五级标题
                "<h6",          # h6标签 - 六级标题
                "<span",        # span标签 - 行内元素容器
                "<table",       # table标签 - 表格
                "<tr",          # tr标签 - 表格行
                "<td",          # td标签 - 表格数据单元格
                "<th",          # th标签 - 表格标题单元格
                "<ul",          # ul标签 - 无序列表
                "<ol",          # ol标签 - 有序列表
                "<header",      # header标签 - 页面或区域头部
                "<footer",      # footer标签 - 页面或区域底部
                "<nav",         # nav标签 - 导航区域
                # Head
                # 文档头部相关标签
                "<head",        # head标签 - 文档头部
                "<style",       # style标签 - 内联样式
                "<script",      # script标签 - JavaScript脚本
                "<meta",        # meta标签 - 元数据
                "<title",       # title标签 - 文档标题
                "",             # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.CSHARP:
            # C#语言的分隔符列表（按优先级从高到低）
            return [
                # C#特有的类型定义（最高优先级）
                "\ninterface ",  # 接口定义 - C#的重要抽象机制
                "\nenum ",       # 枚举定义 - 定义命名常量集合
                "\nimplements ", # 接口实现（注：C#实际使用冒号，这可能是错误）
                "\ndelegate ",   # 委托定义 - C#的函数指针机制
                "\nevent ",      # 事件定义 - C#的事件驱动编程
                # Split along class definitions
                # 按类定义分割
                "\nclass ",      # 类定义 - C#面向对象编程的核心
                "\nabstract ",   # 抽象类定义
                # Split along method definitions
                # 按方法定义分割（通过访问修饰符识别）
                "\npublic ",     # public访问修饰符
                "\nprotected ",  # protected访问修饰符
                "\nprivate ",    # private访问修饰符
                "\nstatic ",     # static静态修饰符
                "\nreturn ",     # return语句 - 方法返回
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",         # if条件语句
                "\ncontinue ",   # continue语句 - 跳过当前循环迭代
                "\nfor ",        # for循环语句
                "\nforeach ",    # foreach循环 - C#的增强for循环
                "\nwhile ",      # while循环语句
                "\nswitch ",     # switch选择语句
                "\nbreak ",      # break语句 - 跳出循环或switch
                "\ncase ",       # case分支语句
                "\nelse ",       # else分支语句
                # Split by exceptions
                # 按异常处理分割
                "\ntry ",        # try异常处理块
                "\nthrow ",      # throw抛出异常
                "\nfinally ",    # finally最终执行块
                "\ncatch ",      # catch异常捕获
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",          # 双换行 - 段落分隔
                "\n",            # 单换行 - 行分隔
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.SOL:
            # Solidity语言的分隔符列表（按优先级从高到低）
            return [
                # Split along compiler information definitions
                # 按编译器信息定义分割
                "\npragma ",     # pragma指令 - 指定编译器版本和设置
                "\nusing ",      # using指令 - 库的使用声明
                # Split along contract definitions
                # 按合约定义分割
                "\ncontract ",   # 合约定义 - Solidity的核心结构
                "\ninterface ",  # 接口定义 - 定义合约接口
                "\nlibrary ",    # 库定义 - 可重用代码库
                # Split along method definitions
                # 按方法定义分割
                "\nconstructor ", # 构造函数 - 合约初始化函数
                "\ntype ",       # 类型定义 - 自定义类型
                "\nfunction ",   # 函数定义 - 合约中的函数
                "\nevent ",      # 事件定义 - 区块链事件日志
                "\nmodifier ",   # 修饰符定义 - 函数修饰符
                "\nerror ",      # 错误定义 - 自定义错误类型
                "\nstruct ",     # 结构体定义 - 自定义数据结构
                "\nenum ",       # 枚举定义 - 枚举类型
                # Split along control flow statements
                # 按控制流语句分割
                "\nif ",         # if条件语句
                "\nfor ",        # for循环语句
                "\nwhile ",      # while循环语句
                "\ndo while ",   # do-while循环语句
                "\nassembly ",   # 内联汇编块 - 低级操作
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n\n",          # 双换行 - 段落分隔
                "\n",            # 单换行 - 行分隔
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]
        elif language == Language.COBOL:
            # COBOL语言的分隔符列表（按优先级从高到低）
            return [
                # Split along divisions
                # 按COBOL的四大部分分割（最高优先级）
                "\nIDENTIFICATION DIVISION.", # 标识部 - 程序标识信息
                "\nENVIRONMENT DIVISION.",    # 环境部 - 程序运行环境配置
                "\nDATA DIVISION.",           # 数据部 - 数据定义和文件描述
                "\nPROCEDURE DIVISION.",      # 过程部 - 程序逻辑和处理过程
                # Split along sections within DATA DIVISION
                # 按数据部内的节分割
                "\nWORKING-STORAGE SECTION.", # 工作存储节 - 程序变量定义
                "\nLINKAGE SECTION.",         # 连接节 - 参数传递定义
                "\nFILE SECTION.",            # 文件节 - 文件记录描述
                # Split along sections within PROCEDURE DIVISION
                # 按过程部内的节分割
                "\nINPUT-OUTPUT SECTION.",    # 输入输出节 - I/O控制
                # Split along paragraphs and common statements
                # 按段落和常用语句分割
                "\nOPEN ",       # OPEN语句 - 打开文件
                "\nCLOSE ",      # CLOSE语句 - 关闭文件
                "\nREAD ",       # READ语句 - 读取记录
                "\nWRITE ",      # WRITE语句 - 写入记录
                "\nIF ",         # IF语句 - 条件判断
                "\nELSE ",       # ELSE语句 - 条件分支
                "\nMOVE ",       # MOVE语句 - 数据移动/赋值
                "\nPERFORM ",    # PERFORM语句 - 执行段落或子程序
                "\nUNTIL ",      # UNTIL语句 - 循环条件
                "\nVARYING ",    # VARYING语句 - 循环变量控制
                "\nACCEPT ",     # ACCEPT语句 - 接受输入
                "\nDISPLAY ",    # DISPLAY语句 - 显示输出
                "\nSTOP RUN.",   # STOP RUN语句 - 程序终止
                # Split by the normal type of lines
                # 按常规行类型分割
                "\n",            # 单换行 - 行分隔（COBOL通常不使用双换行）
                " ",             # 空格 - 单词分隔
                "",              # 空字符串 - 字符级分割（最后手段）
            ]

        else:
            # 如果提供的语言不在支持列表中，抛出错误
            raise ValueError(
                f"语言 {language} 不被支持！"
                f"请从以下支持的语言中选择：{list(Language)}"
            )

# =============================================================================
# 递归Token分块器使用说明和最佳实践
# =============================================================================

"""
递归Token分块器的设计理念和使用指南：

1. 设计理念：
   - 保持文本的自然结构和语义完整性
   - 按照语言特定的语法结构进行智能分割
   - 递归处理，确保所有文本块都在合适的大小范围内

2. 分隔符优先级原则：
   - 高优先级：语言特定的结构（类、函数、方法定义）
   - 中优先级：控制流语句（if、for、while等）
   - 低优先级：通用分隔符（段落、句子、单词）
   - 最低优先级：字符级分割（最后手段）

3. 使用建议：
   - 对于代码文档：使用对应的编程语言设置
   - 对于通用文本：使用默认分隔符设置
   - 对于特殊格式：使用对应的格式设置（Markdown、LaTeX等）

4. 参数调优：
   - chunk_size：根据下游任务的输入限制设置
   - chunk_overlap：设置适当的重叠以保持上下文连续性
   - keep_separator：根据是否需要保留结构信息决定

5. 性能特点：
   - 时间复杂度：O(n)，其中n是文本长度
   - 空间复杂度：O(k)，其中k是分块数量
   - 适用场景：中等规模文本的高质量分块需求
"""
