"""
固定Token分块器模块

此脚本改编自 LangChain 包，由 LangChain AI 开发
原始代码可在以下位置找到: https://github.com/langchain-ai/langchain/blob/master/libs/text-splitters/langchain_text_splitters/base.py

这个模块实现了基于固定token数量的文本分块功能，主要用于将长文本按照指定的token数量进行分割。
特别适用于需要控制输入到语言模型的文本长度的场景。
"""

# 导入必要的模块和类型
from abc import ABC, abstractmethod  # 抽象基类相关
from enum import Enum  # 枚举类型
import logging  # 日志记录
from typing import (  # 类型提示相关
    AbstractSet,  # 抽象集合类型
    Any,  # 任意类型
    Callable,  # 可调用对象类型
    Collection,  # 集合类型
    Iterable,  # 可迭代对象类型
    List,  # 列表类型
    Literal,  # 字面量类型
    Optional,  # 可选类型
    Sequence,  # 序列类型
    Type,  # 类型类型
    TypeVar,  # 类型变量
    Union,  # 联合类型
)
from .base_chunker import BaseChunker  # 导入基础分块器类

# 移除了dataclass导入，因为不再使用token-based分割

# 创建日志记录器，用于记录模块运行时的信息
logger = logging.getLogger(__name__)

TS = TypeVar("TS", bound="TextSplitter")
class TextSplitter(BaseChunker, ABC):
    """
    文本分割器的抽象接口类
    
    这个类提供了文本分割的基础功能和通用方法。它继承自 BaseChunker
    并添加了更多的配置选项和辅助方法。
    
    主要功能:
    - 定义文本分割的基本参数（块大小、重叠等）
    - 提供文本合并和分割的通用逻辑
    - 支持自定义长度计算函数
    - 提供 tiktoken 编码器的集成
    
    属性:
        _chunk_size (int): 每个文本块的目标大小
        _chunk_overlap (int): 相邻文本块之间的重叠大小
        _length_function (Callable): 用于计算文本长度的函数
        _keep_separator (bool): 是否保留分隔符
        _add_start_index (bool): 是否添加起始索引信息
        _strip_whitespace (bool): 是否去除空白字符
    """

    def __init__(
        self,
        chunk_size: int = 4000,
        chunk_overlap: int = 200,
        length_function: Callable[[str], int] = len,
        keep_separator: bool = False,
        add_start_index: bool = False,
        strip_whitespace: bool = True,
    ) -> None:
        """
        初始化文本分割器
        
        参数:
            chunk_size (int): 每个文本块的目标大小，默认4000
            chunk_overlap (int): 相邻文本块的重叠大小，默认200
            length_function (Callable): 计算文本长度的函数，默认使用len()
            keep_separator (bool): 是否在分割时保留分隔符，默认False
            add_start_index (bool): 是否为每个块添加起始位置索引，默认False
            strip_whitespace (bool): 是否去除文本块首尾的空白字符，默认True
        """
        # 验证参数的有效性
        if chunk_overlap > chunk_size:
            raise ValueError(
                f"Got a larger chunk overlap ({chunk_overlap}) than chunk size "
                f"({chunk_size}), should be smaller."
            )
        
        # 初始化实例属性
        self._chunk_size = chunk_size
        self._chunk_overlap = chunk_overlap
        self._length_function = length_function
        self._keep_separator = keep_separator
        self._add_start_index = add_start_index
        self._strip_whitespace = strip_whitespace

    @abstractmethod
    def split_text(self, text: str) -> List[str]:
        """
        将文本分割成多个组件
        
        这是抽象方法，子类必须实现具体的分割逻辑
        
        参数:
            text (str): 待分割的文本
            
        返回:
            List[str]: 分割后的文本块列表
        """

    def _join_docs(self, docs: List[str], separator: str) -> Optional[str]:
        """
        使用指定分隔符连接文档列表
        
        参数:
            docs (List[str]): 要连接的文档列表
            separator (str): 用于连接的分隔符
            
        返回:
            Optional[str]: 连接后的文本，如果结果为空则返回None
        """
        text = separator.join(docs)
        if self._strip_whitespace:
            text = text.strip() # 去除首尾空白字符
        if text == "":
            return None
        else:
            return text

    def _merge_splits(self, splits: Iterable[str], separator: str) -> List[str]:
        """
        将小的文本片段合并成中等大小的文本块
        
        这个方法的核心逻辑是将较小的文本片段智能地合并成适合发送给LLM的中等大小文本块。
        它会考虑块大小限制、重叠要求，并确保不会创建过大的文本块。
        
        参数:
            splits (Iterable[str]): 待合并的文本片段迭代器
            separator (str): 用于连接文本片段的分隔符
            
        返回:
            List[str]: 合并后的文本块列表
        """
        # We now want to combine these smaller pieces into medium size
        # chunks to send to the LLM.
        separator_len = self._length_function(separator)  # 计算分隔符的长度

        docs = []  # 存储最终的文档块
        current_doc: List[str] = []  # 当前正在构建的文档块
        total = 0  # 当前文档块的总长度
        
        # 遍历每个文本片段
        for d in splits:
            _len = self._length_function(d)  # 计算当前片段的长度
            
            # 检查添加当前片段是否会超过块大小限制
            if (
                total + _len + (separator_len if len(current_doc) > 0 else 0)
                > self._chunk_size
            ):
                # 如果当前块已经超过了指定大小，记录警告
                if total > self._chunk_size:
                    logger.warning(
                        f"Created a chunk of size {total}, "
                        f"which is longer than the specified {self._chunk_size}"
                    )
                
                # 如果当前文档块不为空，将其加入到结果中
                if len(current_doc) > 0:
                    doc = self._join_docs(current_doc, separator)
                    if doc is not None:
                        docs.append(doc)
                    
                    # 处理重叠逻辑：移除文档开头的片段直到满足以下条件之一：
                    # - 当前块大小小于等于重叠大小
                    # - 或者添加新片段后不会超过块大小限制
                    while total > self._chunk_overlap or (
                        total + _len + (separator_len if len(current_doc) > 0 else 0)
                        > self._chunk_size
                        and total > 0
                    ):
                        # 从当前文档块中移除第一个片段，并更新总长度
                        total -= self._length_function(current_doc[0]) + (
                            separator_len if len(current_doc) > 1 else 0
                        )
                        current_doc = current_doc[1:]  # 移除第一个元素
            
            # 将当前片段添加到当前文档块中
            current_doc.append(d)
            # 更新总长度（包括分隔符的长度）
            total += _len + (separator_len if len(current_doc) > 1 else 0)
        
        # 处理最后一个文档块
        doc = self._join_docs(current_doc, separator)
        if doc is not None:
            docs.append(doc)
        return docs

    # @classmethod
    # def from_huggingface_tokenizer(cls, tokenizer: Any, **kwargs: Any) -> TextSplitter:
    #     """Text splitter that uses HuggingFace tokenizer to count length."""
    #     try:
    #         from transformers import PreTrainedTokenizerBase

    #         if not isinstance(tokenizer, PreTrainedTokenizerBase):
    #             raise ValueError(
    #                 "Tokenizer received was not an instance of PreTrainedTokenizerBase"
    #             )

    #         def _huggingface_tokenizer_length(text: str) -> int:
    #             return len(tokenizer.encode(text))

    #     except ImportError:
    #         raise ValueError(
    #             "Could not import transformers python package. "
    #             "Please install it with `pip install transformers`."
    #         )
    #     return cls(length_function=_huggingface_tokenizer_length, **kwargs)

    @classmethod
    def from_tiktoken_encoder(
        cls: Type[TS],
        encoding_name: str = "gpt2",
        model_name: Optional[str] = None,
        allowed_special: Union[Literal["all"], AbstractSet[str]] = set(),
        disallowed_special: Union[Literal["all"], Collection[str]] = "all",
        **kwargs: Any,
    ) -> TS:
        """
        使用tiktoken编码器创建文本分割器的类方法
        
        这个方法创建一个使用tiktoken编码器来计算文本长度的文本分割器实例。
        tiktoken是OpenAI开发的用于计算GPT模型token数量的库。
        
        参数:
            encoding_name (str): 编码器名称，默认"gpt2"
            model_name (Optional[str]): 模型名称，如果指定则会使用对应模型的编码器
            allowed_special (Union[Literal["all"], AbstractSet[str]]): 允许的特殊token集合
            disallowed_special (Union[Literal["all"], Collection[str]]): 不允许的特殊token集合
            **kwargs: 其他传递给构造函数的参数
            
        返回:
            TS: 配置了tiktoken编码器的文本分割器实例
            
        异常:
            ImportError: 如果tiktoken包未安装
        """
        try:
            import tiktoken  # 尝试导入tiktoken库
        except ImportError:
            raise ImportError(
                "Could not import tiktoken python package. "
                "This is needed in order to calculate max_tokens_for_prompt. "
                "Please install it with `pip install tiktoken`."
            )

        # 根据参数选择合适的编码器
        if model_name is not None:
            enc = tiktoken.encoding_for_model(model_name)  # 根据模型名获取编码器
        else:
            enc = tiktoken.get_encoding(encoding_name)  # 根据编码器名获取编码器

        def _tiktoken_encoder(text: str) -> int:
            """
            使用tiktoken编码器计算文本的token数量
            
            参数:
                text (str): 待计算的文本
                
            返回:
                int: 文本的token数量
            """
            return len(
                enc.encode(
                    text,
                    allowed_special=allowed_special,
                    disallowed_special=disallowed_special,
                )
            )

        # 如果是FixedTokenChunker的子类，添加额外的参数
        if issubclass(cls, FixedTokenChunker):
            extra_kwargs = {
                "encoding_name": encoding_name,
                "model_name": model_name,
                "allowed_special": allowed_special,
                "disallowed_special": disallowed_special,
            }
            kwargs = {**kwargs, **extra_kwargs}  # 合并参数

        return cls(length_function=_tiktoken_encoder, **kwargs)  # 创建实例
    
class FixedTokenChunker(TextSplitter):
    """
    固定Token数量的文本分块器
    
    这个类使用模型的tokenizer将文本分割成固定token数量的块。
    它特别适用于需要精确控制输入到语言模型的token数量的场景。
    
    主要特点:
    - 使用tiktoken库进行精确的token计算
    - 支持多种OpenAI模型的编码器
    - 可以控制特殊token的处理方式
    - 支持块之间的重叠以保持上下文连续性
    
    属性:
        _tokenizer: tiktoken编码器实例
        _allowed_special: 允许的特殊token集合
        _disallowed_special: 不允许的特殊token集合
    """

    def __init__(
        self,
        encoding_name: str = "cl100k_base",
        model_name: Optional[str] = None,
        chunk_size: int = 4000,
        chunk_overlap: int = 200,
        allowed_special: Union[Literal["all"], AbstractSet[str]] = set(),
        disallowed_special: Union[Literal["all"], Collection[str]] = "all",
        **kwargs: Any,
    ) -> None:
        """
        初始化固定Token分块器
        
        参数:
            encoding_name (str): tiktoken编码器名称，默认"cl100k_base"（用于GPT-4）
            model_name (Optional[str]): 模型名称，如果指定则自动选择对应编码器
            chunk_size (int): 每个块的目标token数量，默认4000
            chunk_overlap (int): 相邻块之间的重叠token数量，默认200
            allowed_special (Union[Literal["all"], AbstractSet[str]]): 允许的特殊token
            disallowed_special (Union[Literal["all"], Collection[str]]): 禁止的特殊token
            **kwargs: 传递给父类的其他参数
            
        异常:
            ImportError: 如果tiktoken包未安装
        """
        # 调用父类构造函数
        super().__init__(chunk_size=chunk_size, chunk_overlap=chunk_overlap, **kwargs)
        
        # 尝试导入tiktoken库
        try:
            import tiktoken
        except ImportError:
            raise ImportError(
                "Could not import tiktoken python package. "
                "This is needed in order to for FixedTokenChunker. "
                "Please install it with `pip install tiktoken`."
            )

        # 根据参数选择合适的编码器
        if model_name is not None:
            enc = tiktoken.encoding_for_model(model_name)  # 根据模型名获取编码器
        else:
            enc = tiktoken.get_encoding(encoding_name)  # 根据编码器名获取编码器
            
        # 保存编码器和特殊token设置
        self._tokenizer = enc
        self._allowed_special = allowed_special
        self._disallowed_special = disallowed_special

    def split_text(self, text: str) -> List[str]:
        """
        将文本分割成固定token数量的块

        这个方法使用混合策略：字符级分割 + token长度计算，避免中文字符被破坏。
        参考LangChain的RecursiveCharacterTextSplitter.from_tiktoken_encoder实现。

        参数:
            text (str): 待分割的原始文本

        返回:
            List[str]: 分割后的文本块列表，每个块包含大约指定数量的token
        """
        # 使用字符级分割，但用token计数来控制大小
        return self._split_text_with_token_counting(text)

    def _split_text_with_token_counting(self, text: str) -> List[str]:
        """
        使用字符级分割但token计数的混合方法

        这种方法确保：
        1. 不会破坏Unicode字符（特别是中文字符）
        2. 精确控制token数量
        3. 保持文本的完整性
        """
        if not text:
            return []

        # 定义分隔符优先级（从高到低）
        separators = ["\n\n", "\n", "。", "！", "？", ".", "!", "?", ";", "；", " ", ""]

        return self._recursive_split_with_token_counting(text, separators)

    def _recursive_split_with_token_counting(self, text: str, separators: List[str]) -> List[str]:
        """
        递归分割文本，使用token计数控制大小
        """
        # 如果文本已经足够小，直接返回
        if self._length_function(text) <= self._chunk_size:
            return [text] if text else []

        # 尝试使用分隔符分割
        for separator in separators:
            if separator == "":
                # 最后的分隔符：按字符分割
                return self._split_by_characters_with_token_limit(text)
            elif separator in text:
                # 使用当前分隔符分割
                splits = text.split(separator)
                return self._merge_splits_with_token_counting(splits, separator, separators)

        # 如果没有找到合适的分隔符，按字符分割
        return self._split_by_characters_with_token_limit(text)

    def _merge_splits_with_token_counting(self, splits: List[str], separator: str, separators: List[str]) -> List[str]:
        """
        合并分割片段，使用token计数控制大小
        """
        final_chunks = []
        current_chunk = ""

        for split in splits:
            # 重建完整的片段（包含分隔符）
            if current_chunk:
                test_chunk = current_chunk + separator + split
            else:
                test_chunk = split

            # 检查是否超过大小限制
            if self._length_function(test_chunk) <= self._chunk_size:
                current_chunk = test_chunk
            else:
                # 如果当前块不为空，添加到结果中
                if current_chunk:
                    final_chunks.append(current_chunk)

                # 如果单个split太大，需要进一步分割
                if self._length_function(split) > self._chunk_size:
                    # 递归分割这个过大的片段
                    remaining_separators = separators[separators.index(separator) + 1:] if separator in separators else separators
                    sub_chunks = self._recursive_split_with_token_counting(split, remaining_separators)
                    final_chunks.extend(sub_chunks)
                    current_chunk = ""
                else:
                    current_chunk = split

        # 添加最后的块
        if current_chunk:
            final_chunks.append(current_chunk)

        return final_chunks

    def _split_by_characters_with_token_limit(self, text: str) -> List[str]:
        """
        按字符分割，但确保不超过token限制
        """
        if not text:
            return []

        chunks = []
        current_chunk = ""

        for char in text:
            test_chunk = current_chunk + char
            if self._length_function(test_chunk) <= self._chunk_size:
                current_chunk = test_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk)
                current_chunk = char

        if current_chunk:
            chunks.append(current_chunk)

        return chunks

# 注意：原来的token-based分割方法已被移除，因为它会破坏中文字符
# 现在使用字符级分割 + token计数的混合方法，确保Unicode字符完整性
