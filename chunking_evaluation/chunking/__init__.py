"""
文本分块器模块初始化文件

这个模块包含了多种不同的文本分块算法实现，用于将长文本分割成较小的、有意义的文本块。
这些分块器主要用于RAG（检索增强生成）系统中，帮助提高文本检索和处理的效率。

=== 分块器类型详解 ===

1. FixedTokenChunker - 固定token数量分块器
   📋 特点：按固定token数量进行分块，不考虑语义边界
   ✅ 优势：
      - 执行速度最快，计算开销最小
      - token数量精确控制，适合API限制
      - 实现简单，稳定可靠
   🎯 适用场景：
      - 需要严格控制输入长度的场景
      - 对处理速度要求极高的应用
      - 简单的文本预处理任务
   💡 使用示例：
      chunker = FixedTokenChunker(chunk_size=1000, chunk_overlap=100)
      chunks = chunker.split_text("长文本内容...")

2. RecursiveTokenChunker - 递归token分块器
   📋 特点：按分隔符优先级递归分割，尽量保持文本结构完整性
   ✅ 优势：
      - 保持语义完整性，避免句子被截断
      - 支持多种语言和文档格式
      - 可自定义分隔符优先级
   🎯 适用场景：
      - 通用文本分块，适合大多数应用
      - 代码文档分块，保持代码结构
      - 需要平衡速度和质量的场景
   💡 使用示例：
      chunker = RecursiveTokenChunker(
          chunk_size=1000, 
          separators=["\n\n", "\n", ".", " "]
      )

3. ClusterSemanticChunker - 聚类语义分块器
   📋 特点：基于embedding相似度进行聚类分块，保持语义相关内容的连续性
   ✅ 优势：
      - 语义相关内容保持连续性
      - 自动识别主题边界
      - 适应不同类型的文档结构
   🎯 适用场景：
      - 需要语义连贯性的文档处理
      - 学术论文、技术文档分块
      - 知识库构建和维护
   💡 使用示例：
      chunker = ClusterSemanticChunker(
          max_chunk_size=400, 
          min_chunk_size=50,
          embedding_function=embedding_func
      )

4. LLMSemanticChunker - 大语言模型语义分块器
   📋 特点：使用大语言模型智能识别主题边界和语义转换点
   ✅ 优势：
      - 最智能的语义理解能力
      - 适应性强，处理复杂文档结构
      - 能理解上下文和隐含语义关系
   🎯 适用场景：
      - 高质量要求的语义分块任务
      - 复杂文档结构的处理
      - 对分块质量要求极高的应用
   💡 使用示例：
      chunker = LLMSemanticChunker(
          organisation="openai", 
          model_name="gpt-4o",
          max_chunk_size=500
      )

5. KamradtModifiedChunker - Kamradt改进版分块器
   📋 特点：基于余弦距离和二分搜索的语义分块算法
   ✅ 优势：
      - 平衡语义完整性和块大小控制
      - 使用数学方法优化分块边界
      - 相对稳定的分块质量
   🎯 适用场景：
      - 需要精确控制平均块大小的语义分块
      - 科研和实验性应用
      - 对分块一致性要求较高的场景
   💡 使用示例：
      chunker = KamradtModifiedChunker(
          avg_chunk_size=400, 
          min_chunk_size=50,
          embedding_function=embedding_func
      )

=== 选择指南 ===

🚀 性能优先：
   - 最快速度：FixedTokenChunker
   - 平衡速度与质量：RecursiveTokenChunker

🎯 质量优先：
   - 最高语义质量：LLMSemanticChunker
   - 语义连贯性：ClusterSemanticChunker
   - 数学优化：KamradtModifiedChunker

📊 应用场景：
   - 生产环境大规模处理：FixedTokenChunker 或 RecursiveTokenChunker
   - 知识库和RAG系统：ClusterSemanticChunker 或 LLMSemanticChunker
   - 研究和实验：KamradtModifiedChunker 或 LLMSemanticChunker

💰 成本考虑：
   - 免费方案：FixedTokenChunker, RecursiveTokenChunker
   - 需要API调用：ClusterSemanticChunker, LLMSemanticChunker, KamradtModifiedChunker

=== 性能对比 ===

| 分块器 | 速度 | 质量 | 成本 | 复杂度 |
|--------|------|------|------|--------|
| FixedTokenChunker | ⭐⭐⭐⭐⭐ | ⭐⭐ | 免费 | 低 |
| RecursiveTokenChunker | ⭐⭐⭐⭐ | ⭐⭐⭐ | 免费 | 低 |
| ClusterSemanticChunker | ⭐⭐⭐ | ⭐⭐⭐⭐ | 中等 | 中 |
| LLMSemanticChunker | ⭐⭐ | ⭐⭐⭐⭐⭐ | 高 | 高 |
| KamradtModifiedChunker | ⭐⭐⭐ | ⭐⭐⭐⭐ | 中等 | 中 |
"""

# =============================================================================
# 分块器类导入区域
# =============================================================================

# 基础分块器 - 简单快速的分块方案
from .fixed_token_chunker import FixedTokenChunker  # 固定token数量分块器
from .recursive_token_chunker import RecursiveTokenChunker  # 递归token分块器

# 语义分块器 - 基于语义理解的高级分块方案  
from .cluster_semantic_chunker import ClusterSemanticChunker  # 聚类语义分块器
from .llm_semantic_chunker import LLMSemanticChunker  # 大语言模型语义分块器
from .kamradt_modified_chunker import KamradtModifiedChunker  # Kamradt改进版分块器

# =============================================================================
# 公共接口定义
# =============================================================================

# __all__ = ['ClusterSemanticChunker', 'LLMSemanticChunker']  # 原始的导出列表（已注释）

# 定义当使用 "from chunking import *" 时可以导入的所有类
# 按照功能和复杂度排序，方便用户选择
__all__ = [
    # 基础分块器（推荐优先使用）
    'FixedTokenChunker',        # 最简单快速的分块器
    'RecursiveTokenChunker',    # 平衡速度和质量的分块器
    
    # 语义分块器（高级功能）
    'ClusterSemanticChunker',   # 基于聚类的语义分块器
    'LLMSemanticChunker',       # 基于LLM的智能分块器
    'KamradtModifiedChunker',   # 数学优化的语义分块器
]

# =============================================================================
# 快速使用指南
# =============================================================================

def get_recommended_chunker(use_case: str = "general"):
    """
    根据使用场景推荐合适的分块器
    
    参数:
        use_case (str): 使用场景，可选值：
            - "speed": 优先考虑处理速度
            - "quality": 优先考虑分块质量  
            - "general": 平衡速度和质量（默认）
            - "semantic": 需要语义理解
            - "research": 研究和实验用途
            
    返回:
        str: 推荐的分块器类名
        
    示例:
        recommended = get_recommended_chunker("quality")
        print(f"推荐使用: {recommended}")
    """
    recommendations = {
        "speed": "FixedTokenChunker",
        "quality": "LLMSemanticChunker", 
        "general": "RecursiveTokenChunker",
        "semantic": "ClusterSemanticChunker",
        "research": "KamradtModifiedChunker"
    }
    
    return recommendations.get(use_case, "RecursiveTokenChunker")