from abc import ABC, abstractmethod  # ABC用于创建抽象基类，abstractmethod用于定义抽象方法
from typing import List  # 导入类型提示，用于指定返回值类型

class BaseChunker(ABC):  # 继承ABC使其成为抽象基类
    """
    文本分块器的抽象基类
    
    这是所有文本分块器的基础类，定义了文本分块的标准接口。
    所有具体的分块器实现都必须继承此类并实现 split_text 方法。
    
    用途：
    - 为不同的文本分块策略提供统一的接口
    - 确保所有分块器都实现必要的方法
    - 便于在评估框架中统一处理不同类型的分块器
    """

    @abstractmethod
    def split_text(self, text: str) -> List[str]:
        """
        将输入文本分割成多个文本块
        
        这是抽象方法，必须在子类中实现具体的分块逻辑。
        
        参数:
            text (str): 需要分块的原始文本
            
        返回:
            List[str]: 分块后的文本列表，每个元素是一个文本块
            
        注意:
            - 子类必须实现此方法
            - 返回的文本块应该保持原文的语义完整性
            - 文本块的大小和分割策略由具体实现决定
        """
        pass