"""
LLM语义分块器模块

这个模块实现了基于大语言模型（LLM）的语义分块功能。
它使用LLM来智能识别文本中的主题边界，从而实现更准确的语义分块。
支持OpenAI和Anthropic两种LLM提供商。
"""

# 导入必要的模块和类型
from .base_chunker import BaseChunker  # 基础分块器类
from chunking_evaluation.utils import openai_token_count  # OpenAI token计数函数
from chunking_evaluation.chunking import RecursiveTokenChunker  # 递归分块器
import anthropic  # Anthropic API客户端
import os  # 操作系统接口
import backoff  # 重试装饰器
from tqdm import tqdm  # 进度条显示

class AnthropicClient:
    """
    Anthropic API客户端类
    
    这个类封装了与Anthropic Claude模型的交互逻辑，提供了统一的接口
    来调用Claude模型进行文本分析和处理。
    
    主要功能:
    - 初始化Anthropic客户端连接
    - 提供重试机制的消息创建功能
    - 处理API调用异常和错误恢复
    
    属性:
        client: Anthropic API客户端实例
        model_name (str): 使用的Claude模型名称
    """
    
    def __init__(self, model_name, api_key=None):
        """
        初始化Anthropic客户端
        
        参数:
            model_name (str): Claude模型名称（如"claude-3-5-sonnet-20240620"）
            api_key (str, optional): Anthropic API密钥，如果不提供则使用环境变量
        """
        self.client = anthropic.Anthropic(api_key=api_key)  # 创建Anthropic客户端实例
        self.model_name = model_name  # 保存模型名称

    @backoff.on_exception(backoff.expo, Exception, max_tries=3)
    def create_message(self, system_prompt, messages, max_tokens=1000, temperature=1.0):
        """
        创建消息并调用Claude模型
        
        这个方法使用指数退避重试机制来调用Claude模型，确保在网络问题
        或临时API错误时能够自动重试。
        
        参数:
            system_prompt (str): 系统提示词，定义模型的角色和任务
            messages (List[dict]): 对话消息列表
            max_tokens (int): 最大生成token数量，默认1000
            temperature (float): 生成温度，控制随机性，默认1.0
            
        返回:
            str: Claude模型生成的响应文本
            
        异常:
            Exception: 如果重试3次后仍然失败，抛出最后一次的异常
            
        重试机制:
            - 使用指数退避策略（exponential backoff）
            - 最多重试3次
            - 每次重试间隔会逐渐增加
        """
        try:
            # 调用Anthropic API创建消息
            message = self.client.messages.create(
                model=self.model_name,  # 指定模型
                max_tokens=max_tokens,  # 最大token数
                temperature=temperature,  # 生成温度
                system=system_prompt,  # 系统提示
                messages=messages  # 对话消息
            )
            return message.content[0].text  # 返回生成的文本内容
        except Exception as e:
            print(f"Error occurred: {e}, retrying...")  # 打印错误信息
            raise e  # 重新抛出异常以触发重试机制
        
class OpenAIClient:
    """
    OpenAI API客户端类
    
    这个类封装了与OpenAI GPT模型的交互逻辑，提供了统一的接口
    来调用GPT模型进行文本分析和处理。
    
    主要功能:
    - 初始化OpenAI客户端连接
    - 提供重试机制的消息创建功能
    - 处理API调用异常和错误恢复
    - 格式化消息以符合OpenAI API要求
    
    属性:
        client: OpenAI API客户端实例
        model_name (str): 使用的GPT模型名称
    """
    
    def __init__(self, model_name, api_key=None):
        """
        初始化OpenAI客户端
        
        参数:
            model_name (str): GPT模型名称（如"gpt-4o"）
            api_key (str, optional): OpenAI API密钥，如果不提供则使用环境变量
        """
        from openai import OpenAI  # 导入OpenAI客户端
        self.client = OpenAI(api_key=api_key)  # 创建OpenAI客户端实例
        self.model_name = model_name  # 保存模型名称

    @backoff.on_exception(backoff.expo, Exception, max_tries=3)
    def create_message(self, system_prompt, messages, max_tokens=1000, temperature=1.0):
        """
        创建消息并调用GPT模型
        
        这个方法使用指数退避重试机制来调用GPT模型，确保在网络问题
        或临时API错误时能够自动重试。与Anthropic不同，OpenAI需要将
        系统提示和用户消息合并到一个消息列表中。
        
        参数:
            system_prompt (str): 系统提示词，定义模型的角色和任务
            messages (List[dict]): 对话消息列表
            max_tokens (int): 最大生成token数量，默认1000
            temperature (float): 生成温度，控制随机性，默认1.0
            
        返回:
            str: GPT模型生成的响应文本
            
        异常:
            Exception: 如果重试3次后仍然失败，抛出最后一次的异常
            
        重试机制:
            - 使用指数退避策略（exponential backoff）
            - 最多重试3次
            - 每次重试间隔会逐渐增加
        """
        try:
            # 构建符合OpenAI API格式的消息列表
            # 将系统提示作为第一条消息，然后添加其他消息
            gpt_messages = [
                {"role": "system", "content": system_prompt}  # 系统消息
            ] + messages  # 用户消息

            # 调用OpenAI API创建聊天完成
            completion = self.client.chat.completions.create(
                model=self.model_name,  # 指定模型
                max_tokens=max_tokens,  # 最大token数
                messages=gpt_messages,  # 消息列表
                temperature=temperature  # 生成温度
            )

            # 返回生成的文本内容
            return completion.choices[0].message.content
        except Exception as e:
            print(f"Error occurred: {e}, retrying...")  # 打印错误信息
            raise e  # 重新抛出异常以触发重试机制


class LLMSemanticChunker(BaseChunker):
    """
    LLM语义分块器类
    
    这个类设计用于基于大语言模型（LLM）的建议将文本分割成主题一致的部分。
    它利用LLM的语言理解能力来识别文本中的主题边界，从而实现更智能的语义分块。
    
    工作原理:
    1. 首先将文本分割成小的块
    2. 将这些块标记并发送给LLM
    3. LLM分析文本内容并识别主题转换点
    4. 根据LLM的建议重新组合文本块
    
    主要特点:
    - 支持OpenAI和Anthropic两种LLM提供商
    - 使用LLM的语言理解能力进行智能分块
    - 保持主题的连贯性和一致性
    - 自动处理API调用和错误重试
    
    优势:
    - 比传统方法更准确地识别主题边界
    - 适应不同类型和风格的文本
    - 能够理解复杂的语义关系
    
    参数:
        organisation (str): 要使用的LLM提供商，选项为"openai"（默认）或"anthropic"
        api_key (str, optional): 所选LLM提供商的API密钥，如果不提供则使用默认环境密钥
        model_name (str, optional): 要使用的具体模型。OpenAI默认为"gpt-4o"，Anthropic默认为"claude-3-5-sonnet-20240620"
    
    属性:
        client: LLM客户端实例（OpenAIClient或AnthropicClient）
        splitter: 用于初步分割的递归分块器
    """
    
    def __init__(self, organisation:str="openai", api_key:str=None, model_name:str=None):
        """
        初始化LLM语义分块器
        
        参数:
            organisation (str): LLM提供商，"openai"或"anthropic"，默认"openai"
            api_key (str, optional): API密钥，如果不提供则使用环境变量
            model_name (str, optional): 模型名称，如果不提供则使用默认模型
            
        异常:
            ValueError: 如果提供的organisation不是"openai"或"anthropic"
        """
        # 根据选择的组织初始化相应的客户端
        if organisation == "openai":
            # 设置OpenAI的默认模型
            if model_name is None:
                model_name = "gpt-4o"
            self.client = OpenAIClient(model_name, api_key=api_key)
        elif organisation == "anthropic":
            # 设置Anthropic的默认模型
            if model_name is None:
                model_name = "claude-3-5-sonnet-20240620"
            self.client = AnthropicClient(model_name, api_key=api_key)
        else:
            # 如果提供了无效的组织名称，抛出错误
            raise ValueError("Invalid organisation. Please choose either 'openai' or 'anthropic'.")

        # 创建递归分块器用于初步分割文本
        # 使用较小的块大小（50 tokens）以便LLM能够更精确地分析
        self.splitter = RecursiveTokenChunker(
            chunk_size=50,  # 小块大小，便于LLM分析
            chunk_overlap=0,  # 不设置重叠，避免重复内容
            length_function=openai_token_count  # 使用OpenAI的token计数函数
            )

    def get_prompt(self, chunked_input, current_chunk=0, invalid_response=None):
        """
        构建发送给LLM的提示消息
        
        这个方法创建用于指导LLM进行文本分块的提示消息。提示包含了详细的
        任务说明和格式要求，确保LLM能够准确理解任务并返回正确格式的结果。
        
        参数:
            chunked_input (str): 已标记的文本块，包含<|start_chunk_X|>和<|end_chunk_X|>标签
            current_chunk (int): 当前处理的块编号，默认0
            invalid_response: 之前的无效响应，用于重试时的提示
            
        返回:
            List[dict]: 包含系统消息和用户消息的消息列表
            
        提示设计要点:
            1. 明确任务目标：识别主题边界
            2. 解释输入格式：标记的文本块
            3. 指定输出格式：split_after: 数字列表
            4. 强调约束条件：升序排列、至少一个分割点
            5. 处理错误重试：避免重复无效响应
        """
        messages = [
            {
                "role": "system", 
                "content": (
                    # 系统角色定义：专门用于文本主题分割的助手
                    "You are an assistant specialized in splitting text into thematically consistent sections. "
                    # 输入格式说明：文本已被分成带标签的块
                    "The text has been divided into chunks, each marked with <|start_chunk_X|> and <|end_chunk_X|> tags, where X is the chunk number. "
                    # 任务说明：识别分割点，保持相似主题的块在一起
                    "Your task is to identify the points where splits should occur, such that consecutive chunks of similar themes stay together. "
                    # 输出示例：如果块1和2属于一起，但块3开始新主题，则在块2后分割
                    "Respond with a list of chunk IDs where you believe a split should be made. For example, if chunks 1 and 2 belong together but chunk 3 starts a new topic, you would suggest a split after chunk 2. THE CHUNKS MUST BE IN ASCENDING ORDER."
                    # 输出格式要求：必须使用指定格式
                    "Your response should be in the form: 'split_after: 3, 5'."
                )
            },
            {
                "role": "user", 
                "content": (
                    # 提供标记的文本内容
                    "CHUNKED_TEXT: " + chunked_input + "\n\n"
                    # 输出要求：只返回分割点ID，必须至少有一个分割，必须升序且大于等于当前块
                    "Respond only with the IDs of the chunks where you believe a split should occur. YOU MUST RESPOND WITH AT LEAST ONE SPLIT. THESE SPLITS MUST BE IN ASCENDING ORDER AND EQUAL OR LARGER THAN: " + str(current_chunk)+"." + 
                    # 错误重试提示：如果之前的响应无效，提醒不要重复
                    (f"\n\The previous response of {invalid_response} was invalid. DO NOT REPEAT THIS ARRAY OF NUMBERS. Please try again." if invalid_response else "")
                )
            },
        ]
        return messages

    def split_text(self, text):
        """
        使用LLM进行智能语义分块的主要方法
        
        这个方法实现了完整的LLM语义分块流程：
        1. 初步分割文本成小块
        2. 批量发送给LLM进行主题分析
        3. 根据LLM的建议确定分割点
        4. 重新组合文本块生成最终结果
        
        参数:
            text (str): 待分割的原始文本
            
        返回:
            List[str]: 按主题分组的文本块列表
            
        算法流程:
            1. 使用递归分块器将文本分割成小块
            2. 逐批处理块，每批不超过800个token
            3. 为每批块添加标记并发送给LLM
            4. 解析LLM响应获取分割点
            5. 验证响应格式并处理错误
            6. 根据分割点重新组合文本块
        """
        import re  # 导入正则表达式模块用于解析LLM响应

        # 步骤1: 使用递归分块器将文本分割成小块
        chunks = self.splitter.split_text(text)

        split_indices = []  # 存储所有的分割点索引

        short_cut = len(split_indices) > 0  # 快捷方式标志（当前未使用）

        from tqdm import tqdm  # 导入进度条

        current_chunk = 0  # 当前处理的块索引

        # 使用进度条显示处理进度
        with tqdm(total=len(chunks), desc="Processing chunks") as pbar:
            # 主处理循环：逐批处理文本块
            while True and not short_cut:
                # 如果接近文本末尾，停止处理
                if current_chunk >= len(chunks) - 4:
                    break

                token_count = 0  # 当前批次的token计数
                chunked_input = ''  # 构建发送给LLM的标记文本

                # 步骤2: 构建当前批次的文本（不超过800 tokens）
                for i in range(current_chunk, len(chunks)):
                    token_count += openai_token_count(chunks[i])  # 累计token数
                    # 为每个块添加开始和结束标记
                    chunked_input += f"<|start_chunk_{i+1}|>{chunks[i]}<|end_chunk_{i+1}|>"
                    # 如果超过800 tokens，停止添加更多块
                    if token_count > 800:
                        break

                # 步骤3: 获取LLM的分割建议
                messages = self.get_prompt(chunked_input, current_chunk)
                
                # 重试循环：确保获得有效的LLM响应
                while True:
                    # 调用LLM获取响应
                    result_string = self.client.create_message(
                        messages[0]['content'], 
                        messages[1:], 
                        max_tokens=200,  # 限制响应长度
                        temperature=0.2  # 使用较低温度确保一致性
                    )
                    
                    # 步骤4: 解析LLM响应
                    # Use regular expression to find all numbers in the string
                    # 查找包含"split_after:"的行
                    split_after_line = [line for line in result_string.split('\n') if 'split_after:' in line][0]
                    # 使用正则表达式提取所有数字
                    numbers = re.findall(r'\d+', split_after_line)
                    # Convert the found numbers to integers
                    # 将字符串数字转换为整数
                    numbers = list(map(int, numbers))

                    # print(numbers)  # 调试用（已注释）

                    # 步骤5: 验证响应格式
                    # Check if the numbers are in ascending order and are equal to or larger than current_chunk
                    # 检查数字是否按升序排列且大于等于当前块索引
                    if not (numbers != sorted(numbers) or any(number < current_chunk for number in numbers)):
                        break  # 响应有效，退出重试循环
                    else:
                        # 响应无效，重新生成提示并重试
                        messages = self.get_prompt(chunked_input, current_chunk, numbers)
                        print("Response: ", result_string)
                        print("Invalid response. Please try again.")

                # 步骤6: 保存有效的分割点
                split_indices.extend(numbers)  # 添加到总分割点列表

                current_chunk = numbers[-1]  # 更新当前处理位置

                # 如果没有获得分割点，停止处理
                if len(numbers) == 0:
                    break

                # 更新进度条
                pbar.update(current_chunk - pbar.n)

        pbar.close()  # 关闭进度条

        # 步骤7: 根据分割点重新组合文本块
        # 将分割点索引转换为实际的块索引（减1是因为split_after表示在某块之后分割）
        chunks_to_split_after = [i - 1 for i in split_indices]

        docs = []  # 存储最终的文档块
        current_chunk = ''  # 当前正在构建的文档块
        
        # 遍历所有原始块，根据分割点进行重新组合
        for i, chunk in enumerate(chunks):
            current_chunk += chunk + ' '  # 将当前块添加到正在构建的文档中
            
            # 如果当前位置是分割点，完成当前文档并开始新文档
            if i in chunks_to_split_after:
                docs.append(current_chunk.strip())  # 添加完成的文档（去除首尾空格）
                current_chunk = ''  # 重置当前文档
        
        # 处理最后一个文档（如果有剩余内容）
        if current_chunk:
            docs.append(current_chunk.strip())

        return docs