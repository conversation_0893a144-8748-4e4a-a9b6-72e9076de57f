# 文本分块评估包

## 主要功能

- **分块方法比较**：评估和比较各种流行的分块策略。
- **新颖分块方法**：实现了新的分块方法，如`ClusterSemanticChunker`（聚类语义分块器）和`LLMChunker`（大语言模型分块器）。
- **评估框架**：用于生成特定领域数据集并在AI应用场景中评估检索质量的工具。

## 快速开始
# 评估自定义分块器

<!-- 
这个示例展示了如何实现自己的分块逻辑并评估其性能。
通过继承BaseChunker类，您可以轻松创建符合特定需求的分块策略。
-->

This example shows how to implement your own chunking logic and evaluate its performance.

```python
from chunking_evaluation import BaseChunker, GeneralEvaluation
from chromadb.utils import embedding_functions

# 定义自定义分块类
class CustomChunker(BaseChunker):
    def split_text(self, text):
        # 自定义分块逻辑：每1200个字符分割一次
        return [text[i:i+1200] for i in range(0, len(text), 1200)]

# 实例化自定义分块器和评估器
chunker = CustomChunker()
evaluation = GeneralEvaluation()

# 选择嵌入函数（需要设置有效的OpenAI API密钥）
default_ef = embedding_functions.OpenAIEmbeddingFunction(
    api_key="OPENAI_API_KEY",  # 替换为您的OpenAI API密钥
    model_name="text-embedding-3-large"
)

# 评估分块器性能
results = evaluation.run(chunker, default_ef)

print(results)
# 输出示例结果：
# {'iou_mean': 0.17715979570301696, 'iou_std': 0.10619791407460026, 
# 'recall_mean': 0.8091207841640163, 'recall_std': 0.3792297991952294}
# 
# 结果解释：
# - iou_mean: 平均IoU（交并比）分数
# - iou_std: IoU分数的标准差
# - recall_mean: 平均召回率
# - recall_std: 召回率的标准差
```

# 评估自定义嵌入函数

<!-- 
除了自定义分块器，您还可以实现自己的嵌入函数来测试不同的向量化策略。
这对于比较不同嵌入模型的效果或实现特定领域的嵌入逻辑非常有用。
-->

```python
from chromadb import Documents, EmbeddingFunction, Embeddings

class MyEmbeddingFunction(EmbeddingFunction):
    def __call__(self, input: Documents) -> Embeddings:
        # 在这里实现您的文档嵌入逻辑
        # 例如：使用本地模型、特定的预处理步骤等
        return embeddings

# 实例化嵌入函数
default_ef = MyEmbeddingFunction()

# 使用分块器评估嵌入函数的效果
results = evaluation.run(chunker, default_ef)

# 注意：自定义嵌入函数需要返回与ChromaDB兼容的嵌入向量格式
# 向量维度应保持一致，通常为1536维（OpenAI）或其他固定维度
```

# ClusterSemanticChunker的使用和评估

<!-- 
这个示例演示了如何使用 ClusterSemanticChunker（聚类语义分块器）。
该分块器基于语义相似度进行聚类，能够保持语义相关内容的连续性，
相比简单的固定长度分块，通常能获得更好的检索效果。
-->

This example demonstrates how to use ClusterSemanticChunker and how you can evaluate it yourself.

```python
from chunking_evaluation import BaseChunker, GeneralEvaluation
from chunking_evaluation.chunking import ClusterSemanticChunker
from chromadb.utils import embedding_functions

# 实例化评估器
evaluation = GeneralEvaluation()

# 选择嵌入函数（ClusterSemanticChunker需要嵌入函数来计算语义相似度）
default_ef = embedding_functions.OpenAIEmbeddingFunction(
    api_key="OPENAI_API_KEY",  # 替换为您的OpenAI API密钥
    model_name="text-embedding-3-large"
)

# 实例化聚类语义分块器并运行评估
# max_chunk_size=400 设置最大分块大小为400个token
chunker = ClusterSemanticChunker(default_ef, max_chunk_size=400)
results = evaluation.run(chunker, default_ef)

print(results)
# 输出示例结果：
# {'iou_mean': 0.18255175232840098, 'iou_std': 0.12773219595465307, 
# 'recall_mean': 0.8973469551927365, 'recall_std': 0.29042203879923994}
#
# 性能分析：
# - 相比简单分块器，ClusterSemanticChunker通常有更高的召回率
# - IoU分数的提升表明语义分块能更好地保持相关内容的完整性
# - 标准差较小说明性能相对稳定
```

## 特定领域评估的合成数据集管道

<!-- 
合成数据集管道是该包的核心功能之一，允许您基于自己的语料库开发特定领域的评估数据集。
这对于评估分块器在特定行业或应用场景中的表现非常有价值。
整个流程包括：数据生成、质量过滤、去重处理和最终评估。
-->

Here are the steps you can take to develop a sythetic dataset based off your own corpora for domain specific evaluation.

1. **初始化环境**：

    ```python
    from chunking_evaluation import SyntheticEvaluation
    
    # 指定语料库路径和输出CSV文件
    corpora_paths = [
        'path/to/chatlogs.txt',    # 聊天记录文本
        'path/to/finance.txt',     # 金融领域文档
        # 根据需要添加更多语料库文件
        # 'path/to/medical.txt',   # 医疗领域文档
        # 'path/to/legal.txt',     # 法律领域文档
    ]
    queries_csv_path = 'generated_queries_excerpts.csv'  # 生成的问题-摘录对保存路径
    
    # 初始化合成评估器（需要OpenAI API密钥用于生成问题）
    evaluation = SyntheticEvaluation(
        corpora_paths, 
        queries_csv_path, 
        openai_api_key="OPENAI_API_KEY"  # 替换为您的OpenAI API密钥
    )
    ```

2. **生成问题和摘录**：

    ```python
    # 生成问题和摘录，并保存到CSV文件
    # 这个过程会调用OpenAI API，根据语料库内容自动生成相关问题和对应的参考答案
    evaluation.generate_queries_and_excerpts(
        approximate_excerpts=False,  # False=精确模式，True=近似模式
        num_rounds=1,               # 生成轮数，-1表示持续生成
        queries_per_corpus=5        # 每个语料库生成的问题数量
    )
    
    # 注意：生成过程可能需要一些时间，取决于语料库大小和问题数量
    # 建议先用较小的数据集测试，确认效果后再扩大规模
    ```

3. **应用过滤器**：

    ```python
    # 应用过滤器移除质量较差的问题-摘录对
    # threshold=0.36 表示问题与摘录的语义相似度阈值
    evaluation.filter_poor_excerpts(threshold=0.36)
    
    # 应用过滤器移除重复的问题
    # threshold=0.6 表示问题间相似度阈值，超过此值视为重复
    evaluation.filter_duplicates(threshold=0.6)
    
    # 过滤器说明：
    # - filter_poor_excerpts: 确保生成的问题与参考答案有足够的相关性
    # - filter_duplicates: 避免评估数据中的重复问题影响结果准确性
    ```

4. **运行评估**：

    ```python
    from chunking_evaluation import BaseChunker

    # 定义自定义分块类
    class CustomChunker(BaseChunker):
        def split_text(self, text):
            # 自定义分块逻辑：固定1200字符分块
            return [text[i:i+1200] for i in range(0, len(text), 1200)]

    # 实例化自定义分块器
    chunker = CustomChunker()

    # 在过滤后的数据上运行评估
    results = evaluation.run(chunker)
    print("Evaluation Results:", results)
    
    # 结果分析：
    # 合成评估的结果将反映分块器在您特定领域数据上的表现
    # 这比通用评估更能反映实际应用场景中的效果
    ```

5. **可选：如果无法生成问题，尝试近似摘录模式**：

    ```python
    # 生成问题和摘录，使用近似模式
    # 近似模式在某些复杂文档结构中可能有更好的成功率
    evaluation.generate_queries_and_excerpts(approximate_excerpts=True)
    
    # 近似模式 vs 精确模式：
    # - 精确模式：要求AI返回文档中的确切文本片段
    # - 近似模式：允许AI指定文本的大致位置范围
    # - 建议：先尝试精确模式，如果失败率高再使用近似模式
    ```

## 包依赖项

<!-- 
以下依赖项将随包一起自动安装，每个依赖项都有其特定用途：
-->

The following will be installed along with the package:

- **tiktoken** - OpenAI的官方tokenizer，用于精确计算token数量
- **fuzzywuzzy** - 模糊字符串匹配库，用于文本搜索和相似度计算
- **pandas** - 数据处理和分析库，用于管理评估数据和结果
- **numpy** - 数值计算库，用于向量运算和统计分析
- **tqdm** - 进度条库，用于显示长时间运行任务的进度
- **chromadb** - 向量数据库，用于存储和检索文档嵌入向量
- **python-Levenshtein** - 字符串编辑距离计算，提升fuzzywuzzy性能
- **openai** - OpenAI API客户端，用于调用GPT和embedding服务
- **anthropic** - Anthropic API客户端，用于调用Claude模型（可选）
- **attrs** - 类定义辅助库，简化数据类的创建和管理